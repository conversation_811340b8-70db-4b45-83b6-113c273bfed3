//
//  StepsViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/16.
//

import Foundation
import SwiftUI

// MARK: - 步数统计ViewModel
@MainActor
class StepsViewModel: ObservableObject {
    
    // MARK: - Published Properties
    /// 当前选中的时间段
    @Published var selectedPeriod: TimePeriod = .week
    
    /// 当前显示的步数数据
    @Published var currentStepData: [StepData] = []
    
    /// 统计信息
    @Published var statistics: SportsStatistics?
    
    /// 加载状态
    @Published var isLoading: Bool = false
    
    /// 错误信息
    @Published var errorMessage: String?
    
    /// Y轴最大值（用于图表刻度）
    @Published var yAxisMaxValue: Int = 15000
    
    /// Y轴最小值（用于图表刻度）
    @Published var yAxisMinValue: Int = 0
    
    // MARK: - Private Properties
    private let healthManager: HealthManagerProtocol
    
    // MARK: - Initialization
    // 不提供默认值，让调用者传入 HealthManager
    init(healthManager: HealthManagerProtocol) {
        self.healthManager = healthManager
        Task {
            await initializeData()
        }
    }
    
    // MARK: - Public Methods
    
    /// 切换时间段
    func selectPeriod(_ period: TimePeriod) {
        withAnimation(.easeInOut(duration: 0.3)) {
            selectedPeriod = period
            Task {
                await loadStepData()
            }
        }
    }
    
    /// 刷新数据
    func refreshData() async {
        await loadStepData()
    }
    
    /// 获取平均值（用于绘制平均线）
    func getAverageSteps() -> Double {
        return statistics?.averageSteps ?? 0
    }
    
    /// 获取格式化的变化百分比文本
    func getChangePercentageText() -> String {
        guard let changePercentage = statistics?.changePercentage else {
            return ""
        }
        
        let sign = changePercentage >= 0 ? "+" : "-"
        return "\(sign) \(String(format: "%.1f", abs(changePercentage)))%"
    }
    
    /// 获取变化百分比颜色
    func getChangePercentageColor() -> Color {
        guard let changePercentage = statistics?.changePercentage else {
            return .textSecondary
        }
        
        return changePercentage >= 0 ? .success : .error
    }
    
    // MARK: - Private Methods
    
    /// 初始化数据
    private func initializeData() async {
        // 请求权限
        try? await healthManager.requestAuthorization(for: [.steps, .calories])
        
        // 加载数据
        await loadStepData()
    }
    
    /// 加载步数数据
    private func loadStepData() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // 获取健康数据
            let healthData = try await healthManager.fetchHealthData(
                type: .steps,
                period: selectedPeriod
            )
            
            // 转换为 StepData
            currentStepData = healthData.map { $0.toStepData() }
            
            // 计算统计信息
            calculateStatistics()
            
            // 更新Y轴刻度
            updateYAxisScale()
            
        } catch {
            errorMessage = "数据加载失败"
        }
        
        isLoading = false
    }
    
    /// 计算统计信息
    private func calculateStatistics() {
        guard !currentStepData.isEmpty else {
            statistics = nil
            return
        }
        
        // 获取上一周期的平均值用于计算变化百分比
        let previousPeriodAverage = calculatePreviousPeriodAverage()
        
        statistics = SportsStatistics(
            stepData: currentStepData,
            period: selectedPeriod,
            previousPeriodAverage: previousPeriodAverage
        )
    }
    
    /// 计算上一周期的平均值
    private func calculatePreviousPeriodAverage() -> Double? {
        // 简化实现：返回当前平均值的90%-110%作为模拟对比
        let currentAverage = Double(currentStepData.reduce(0) { $0 + $1.steps }) / Double(currentStepData.count)
        let variation = Double.random(in: 0.9...1.1)
        return currentAverage * variation
    }
    
    /// 更新Y轴刻度
    private func updateYAxisScale() {
        guard !currentStepData.isEmpty else {
            yAxisMinValue = 0
            yAxisMaxValue = 15000
            return
        }
        
        let steps = currentStepData.map(\.steps)
        let minSteps = steps.min() ?? 0
        let maxSteps = steps.max() ?? 15000
        
        // 设置Y轴最小值（向下取整到千位）
        yAxisMinValue = max(0, (minSteps / 1000) * 1000 - 1000)
        
        // 设置Y轴最大值（向上取整到千位）
        yAxisMaxValue = ((maxSteps / 1000) + 1) * 1000 + 1000
        
        // 确保最小范围
        if yAxisMaxValue - yAxisMinValue < 5000 {
            yAxisMaxValue = yAxisMinValue + 5000
        }
    }
}

// MARK: - Preview Support
extension StepsViewModel {
    /// 创建用于预览的实例
    static func preview() -> StepsViewModel {
        @StateObject 
        let viewModel = StepsViewModel()
        
        // 生成预览数据
        let calendar = Calendar.current
        let now = Date()
        let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
        
        var previewData: [StepData] = []
        for i in 0..<7 {
            if let date = calendar.date(byAdding: .day, value: i, to: startOfWeek),
               date <= now {
                let steps = Int.random(in: 5000...12000)
                let calories = Double(steps) * 0.04
                previewData.append(StepData(date: date, steps: steps, calories: calories))
            }
        }
        
        viewModel.currentStepData = previewData
        viewModel.calculateStatistics()
        viewModel.updateYAxisScale()
        
        return viewModel
    }
}
