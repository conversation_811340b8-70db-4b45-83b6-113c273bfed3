//
//  HealthManager.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/19.
//

import Foundation
import HealthKit

// MARK: Manager实现
@MainActor
class HealthManager: ObservableObject, HealthManagerProtocol{
    private let healthStore = HKHealthStore()
    
    /// 检查是否可用
    var isHealthKitAvailable: Bool{
        return HKHealthStore.isHealthDataAvailable()
    }
    
    // MARK: Authorization
    func requestAuthorization() async throws {
        guard isHealthKitAvailable else {
            throw HealthKitError.notAvailable
        }
        
        /// 定义需要读取的健康数据类型
        guard let stepType = HKQuantityType.quantityType(forIdentifier: .stepCount),
              let calorieType = HKQuantityType.quantityType(forIdentifier: .activeEnergyBurned) else {
            throw HealthKitError.dataNotAvailable
        }
        
        let readTypes: Set<HKObjectType> = [stepType, calorieType]
        
        do{
            try await healthStore.requestAuthorization(toShare: [], read: readTypes)
            
            // 检查授权状态
            let stepAuthStatus = healthStore.authorizationStatus(for: stepType)
            if stepAuthStatus == .notDetermined || stepAuthStatus == .sharingDenied {
                throw HealthKitError.authorizationDenied
            }
        } catch {
            if error is HealthKitError {
                throw error
            } else {
                throw HealthKitError.queryFailed(error)
            }
        }
    }
        
    /// 检查授权状态
    func checkAuthorizationStatus() -> HKAuthorizationStatus {
        guard let stepType = HKQuantityType.quantityType(forIdentifier: .stepCount) else {
            return .notDetermined
        }
        return healthStore.authorizationStatus(for: stepType)
    }
        
    // MARK: 获取数据
    /// 获取指定日期范围的步数数据
    func fetchStepData(for dateRange: DateRange) async throws -> [StepData] {
        guard isHealthKitAvailable else {
            throw HealthKitError.notAvailable
        }
        
        guard let stepType = HKQuantityType.quantityType(forIdentifier: .stepCount) else {
            throw HealthKitError.dataNotAvailable
        }
        
        // 检查授权状态
        let authStatus = checkAuthorizationStatus()
        guard authStatus == .sharingAuthorized else {
            throw HealthKitError.authorizationDenied
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            // 创建统计查询
            let interval = createDateComponents(for: dateRange.interval, value: dateRange.intervalValue)
            let anchorDate = Calendar.current.startOfDay(for: dateRange.startDate)
            
            let query = HKStatisticsCollectionQuery(
                quantityType: stepType,
                quantitySamplePredicate: nil,
                options: .cumulativeSum,
                anchorDate: anchorDate,
                intervalComponents: interval
            )
            
            query.initialResultsHandler = { _, results, error in
                if let error = error {
                    continuation.resume(throwing: HealthKitError.queryFailed(error))
                    return
                }
                
                guard let results = results else {
                    continuation.resume(throwing: HealthKitError.dataNotAvailable)
                    return
                }
                
                var stepDataArray: [StepData] = []
                
                results.enumerateStatistics(from: dateRange.startDate, to: dateRange.endDate) { statistics, _ in
                    let steps = Int(statistics.sumQuantity()?.doubleValue(for: .count()) ?? 0)
                    let calories = Double(steps) * 0.04 // 估算卡路里
                    
                    let stepData = StepData(
                        date: statistics.startDate,
                        steps: steps,
                        calories: calories
                    )
                    stepDataArray.append(stepData)
                }
                
                continuation.resume(returning: stepDataArray)
            }
            
            healthStore.execute(query)
        }
    }
    
    /// 获取指定日期的小时级步数数据
    func fetchHourlyStepData(for date: Date) async throws -> [StepData] {
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay) ?? date
        
        let dateRange = DateRange(
            startDate: startOfDay,
            endDate: endOfDay,
            interval: .hour,
            intervalValue: 1
        )
        
        return try await fetchStepData(for: dateRange)
    }
    
    // MARK: 辅助方法
    /// 创建日期组件
        private func createDateComponents(for component: Calendar.Component, value: Int) -> DateComponents {
            var components = DateComponents()
            switch component {
            case .hour:
                components.hour = value
            case .day:
                components.day = value
            case .weekOfYear:
                components.weekOfYear = value
            case .month:
                components.month = value
            default:
                components.day = value
            }
            return components
        }
}
