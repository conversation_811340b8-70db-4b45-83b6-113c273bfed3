//
//  HealthManagerProtocol.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/19.
//

import Foundation
import HealthKit

// MARK: - 日期范围结构
struct DateRange {
    let startDate: Date
    let endDate: Date
    let interval: Calendar.Component
    let intervalValue: Int
    
    init(startDate: Date, endDate: Date, interval: Calendar.Component = .day, intervalValue: Int = 1) {
        self.startDate = startDate
        self.endDate = endDate
        self.interval = interval
        self.intervalValue = intervalValue
    }
}

// MARK: - HealthKit 错误类型
enum HealthKitError: LocalizedError {
    case notAvailable
    case authorizationDenied
    case dataNotAvailable
    case queryFailed(Error)
    
    var errorDescription: String? {
        switch self {
        case .notAvailable:
            return "此设备不支持 HealthKit"
        case .authorizationDenied:
            return "健康数据访问权限被拒绝"
        case .dataNotAvailable:
            return "暂无健康数据"
        case .queryFailed(let error):
            return "数据查询失败: \(error.localizedDescription)"
        }
    }
}

// MARK: - HealthManager 协议
protocol HealthManagerProtocol {
    /// 请求 HealthKit 授权
    func requestAuthorization() async throws
    
    /// 检查授权状态
    func checkAuthorizationStatus() -> HKAuthorizationStatus
    
    /// 获取指定日期范围的步数数据
    func fetchStepData(for dateRange: DateRange) async throws -> [StepData]
    
    /// 获取指定日期的小时级步数数据
    func fetchHourlyStepData(for date: Date) async throws -> [StepData]
    
    /// 检查 HealthKit 是否可用
    var isHealthKitAvailable: Bool { get }
}
