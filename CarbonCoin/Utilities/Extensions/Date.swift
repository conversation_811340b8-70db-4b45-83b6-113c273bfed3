//
//  Date.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/19.
//

import Foundation

// MARK: - Date 扩展
extension Date {
    
    /// 获取今天的日期范围（按小时分组）
    static func todayRange() -> DateRange {
        let calendar = Calendar.current
        let now = Date()
        let startOfDay = calendar.startOfDay(for: now)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay) ?? now
        
        return DateRange(
            startDate: startOfDay,
            endDate: endOfDay,
            interval: .hour,
            intervalValue: 1
        )
    }
    
    /// 获取最近7天的日期范围
    static func weekRange() -> DateRange {
        let calendar = Calendar.current
        let now = Date()
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: now) ?? now
        
        return DateRange(
            startDate: calendar.startOfDay(for: weekAgo),
            endDate: now,
            interval: .day,
            intervalValue: 1
        )
    }
    
    /// 获取最近30天的日期范围
    static func monthRange() -> DateRange {
        let calendar = Calendar.current
        let now = Date()
        let monthAgo = calendar.date(byAdding: .day, value: -30, to: now) ?? now
        
        return DateRange(
            startDate: calendar.startOfDay(for: monthAgo),
            endDate: now,
            interval: .day,
            intervalValue: 1
        )
    }
    
    /// 获取最近6个月的日期范围（按周分组）
    static func sixMonthsRange() -> DateRange {
        let calendar = Calendar.current
        let now = Date()
        let sixMonthsAgo = calendar.date(byAdding: .month, value: -6, to: now) ?? now
        
        return DateRange(
            startDate: calendar.startOfDay(for: sixMonthsAgo),
            endDate: now,
            interval: .weekOfYear,
            intervalValue: 1
        )
    }
    
    /// 根据 TimePeriod 获取对应的日期范围
    static func dateRange(for period: TimePeriod) -> DateRange {
        switch period {
        case .day:
            return todayRange()
        case .week:
            return weekRange()
        case .month:
            return monthRange()
        case .sixMonths:
            return sixMonthsRange()
        }
    }
    
    /// 获取日期的显示格式
    func displayString(for period: TimePeriod) -> String {
        let formatter = DateFormatter()
        
        switch period {
        case .day:
            formatter.dateFormat = "HH:mm"
        case .week, .month:
            formatter.dateFormat = "MM/dd"
        case .sixMonths:
            formatter.dateFormat = "MM/dd"
        }
        
        return formatter.string(from: self)
    }
}

// MARK: - Calendar 扩展
extension Calendar {
    
    /// 获取指定日期的开始时间
    func startOfDay(for date: Date) -> Date {
        return self.dateInterval(of: .day, for: date)?.start ?? date
    }
    
    /// 获取指定日期的结束时间
    func endOfDay(for date: Date) -> Date {
        guard let interval = self.dateInterval(of: .day, for: date) else { return date }
        return interval.end.addingTimeInterval(-1) // 减去1秒，避免跨天
    }
}
